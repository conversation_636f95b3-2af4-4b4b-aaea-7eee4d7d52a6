using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface IShiftRepository : IRepository<Shift>
    {
        Task<IEnumerable<Shift>> GetActiveShiftsAsync();
        Task<Shift?> GetCurrentShiftAsync();
    }

    public class ShiftRepository : BaseRepository<Shift>, IShiftRepository
    {
        protected override string TableName => "shifts";
        protected override string SelectColumns => "id, name, start_time, end_time, is_active";

        public ShiftRepository(DatabaseConnection dbConnection) : base(dbConnection) { }

        public override async Task<int> AddAsync(Shift entity)
        {
            var sql = @"INSERT INTO shifts (name, start_time, end_time, is_active) 
                       VALUES (@Name, @StartTime, @EndTime, @IsActive)";
            return await ExecuteInsertAsync(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Shift entity)
        {
            var sql = @"UPDATE shifts SET 
                       name = @Name, 
                       start_time = @StartTime,
                       end_time = @EndTime,
                       is_active = @IsActive
                       WHERE id = @Id";
            return await ExecuteUpdateAsync(sql, entity);
        }

        public async Task<IEnumerable<Shift>> GetActiveShiftsAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"SELECT {SelectColumns} FROM {TableName} WHERE is_active = 1 ORDER BY start_time";
            return await connection.QueryAsync<Shift>(sql);
        }

        public async Task<Shift?> GetCurrentShiftAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var currentTime = DateTime.Now.TimeOfDay;
            
            var sql = @$"SELECT {SelectColumns} FROM {TableName} 
                        WHERE is_active = 1 AND (
                            (start_time <= end_time AND @CurrentTime >= start_time AND @CurrentTime <= end_time) OR
                            (start_time > end_time AND (@CurrentTime >= start_time OR @CurrentTime <= end_time))
                        )";
            
            return await connection.QueryFirstOrDefaultAsync<Shift>(sql, new { CurrentTime = currentTime });
        }
    }
}
