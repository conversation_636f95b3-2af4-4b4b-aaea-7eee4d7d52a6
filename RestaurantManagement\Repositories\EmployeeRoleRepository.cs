using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;
using System.Text.Json;

namespace RestaurantManagement.Repositories
{
    public interface IEmployeeRoleRepository : IRepository<EmployeeRole>
    {
        Task<IEnumerable<EmployeeRole>> GetActiveRolesAsync();
    }

    public class EmployeeRoleRepository : BaseRepository<EmployeeRole>, IEmployeeRoleRepository
    {
        protected override string TableName => "employee_roles";
        protected override string SelectColumns => "id, name, permissions, is_active";

        public EmployeeRoleRepository(DatabaseConnection dbConnection) : base(dbConnection) { }

        public override async Task<IEnumerable<EmployeeRole>> GetAllAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"SELECT {SelectColumns} FROM {TableName} WHERE is_active = 1";
            var roles = await connection.QueryAsync<dynamic>(sql);
            
            return roles.Select(role => new EmployeeRole
            {
                Id = role.id,
                Name = role.name,
                Permissions = JsonSerializer.Deserialize<List<string>>(role.permissions) ?? new List<string>(),
                IsActive = role.is_active
            });
        }

        public override async Task<EmployeeRole?> GetByIdAsync(int id)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"SELECT {SelectColumns} FROM {TableName} WHERE id = @Id";
            var role = await connection.QueryFirstOrDefaultAsync<dynamic>(sql, new { Id = id });
            
            if (role == null) return null;
            
            return new EmployeeRole
            {
                Id = role.id,
                Name = role.name,
                Permissions = JsonSerializer.Deserialize<List<string>>(role.permissions) ?? new List<string>(),
                IsActive = role.is_active
            };
        }

        public override async Task<int> AddAsync(EmployeeRole entity)
        {
            var sql = @"INSERT INTO employee_roles (name, permissions, is_active) 
                       VALUES (@Name, @Permissions, @IsActive)";
            var parameters = new
            {
                entity.Name,
                Permissions = JsonSerializer.Serialize(entity.Permissions),
                entity.IsActive
            };
            return await ExecuteInsertAsync(sql, parameters);
        }

        public override async Task<bool> UpdateAsync(EmployeeRole entity)
        {
            var sql = @"UPDATE employee_roles SET 
                       name = @Name, 
                       permissions = @Permissions,
                       is_active = @IsActive
                       WHERE id = @Id";
            var parameters = new
            {
                entity.Id,
                entity.Name,
                Permissions = JsonSerializer.Serialize(entity.Permissions),
                entity.IsActive
            };
            return await ExecuteUpdateAsync(sql, parameters);
        }

        public async Task<IEnumerable<EmployeeRole>> GetActiveRolesAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"SELECT {SelectColumns} FROM {TableName} WHERE is_active = 1 ORDER BY name";
            var roles = await connection.QueryAsync<dynamic>(sql);
            
            return roles.Select(role => new EmployeeRole
            {
                Id = role.id,
                Name = role.name,
                Permissions = JsonSerializer.Deserialize<List<string>>(role.permissions) ?? new List<string>(),
                IsActive = role.is_active
            });
        }
    }
}
