using System;

namespace RestaurantManagement.Models
{
    public class Employee : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public int RoleId { get; set; }
        public DateTime HireDate { get; set; }
        public decimal Salary { get; set; }
        public decimal CommissionPercentage { get; set; }
        
        // Navigation properties
        public EmployeeRole? Role { get; set; }
        
        // Calculated properties
        public bool HasCommission => CommissionPercentage > 0;
        public int WorkingDays => (DateTime.Now - HireDate).Days;
        public string DisplayName => $"{Name} ({Role?.Name})";
    }
}
