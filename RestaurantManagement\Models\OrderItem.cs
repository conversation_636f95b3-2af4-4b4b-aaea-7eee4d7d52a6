namespace RestaurantManagement.Models
{
    public class OrderItem
    {
        public int Id { get; set; }
        public int OrderId { get; set; }
        public int DishId { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        public string? SpecialInstructions { get; set; }
        
        // Navigation properties
        public Order? Order { get; set; }
        public Dish? Dish { get; set; }
        
        // Calculated properties
        public decimal CalculatedTotalPrice => Quantity * UnitPrice;
    }
}
