namespace RestaurantManagement.Models
{
    public class DishIngredient
    {
        public int Id { get; set; }
        public int DishId { get; set; }
        public int IngredientId { get; set; }
        public decimal Quantity { get; set; }
        public decimal Cost { get; set; }
        
        // Navigation properties
        public Dish? Dish { get; set; }
        public Ingredient? Ingredient { get; set; }
        
        // Calculated properties
        public decimal CalculatedCost => Ingredient != null ? Quantity * Ingredient.CostPerUnit : 0;
    }
}
