using System;

namespace RestaurantManagement.Models
{
    public class EmployeeSales
    {
        public int Id { get; set; }
        public int EmployeeId { get; set; }
        public int OrderId { get; set; }
        public decimal SaleAmount { get; set; }
        public decimal CommissionPercentage { get; set; }
        public decimal CommissionAmount { get; set; }
        public DateTime SaleDate { get; set; }
        
        // Navigation properties
        public Employee? Employee { get; set; }
        public Order? Order { get; set; }
        
        // Calculated properties
        public decimal CalculatedCommissionAmount => SaleAmount * (CommissionPercentage / 100);
    }
}
