namespace RestaurantManagement.Models
{
    public class Ingredient : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public int UnitId { get; set; }
        public decimal CostPerUnit { get; set; }
        public decimal CurrentStock { get; set; }
        public decimal MinimumStock { get; set; }
        
        // Navigation properties
        public Unit? Unit { get; set; }
        
        // Calculated properties
        public bool IsLowStock => CurrentStock <= MinimumStock;
        public decimal TotalValue => CurrentStock * CostPerUnit;
    }
}
