using System;
using System.Collections.Generic;

namespace RestaurantManagement.Models
{
    public enum PurchaseStatus
    {
        Pending,
        Received,
        Cancelled
    }

    public class Purchase
    {
        public int Id { get; set; }
        public int SupplierId { get; set; }
        public DateTime PurchaseDate { get; set; }
        public decimal TotalAmount { get; set; }
        public PurchaseStatus Status { get; set; } = PurchaseStatus.Pending;
        public string? Notes { get; set; }
        public int EmployeeId { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        // Navigation properties
        public Supplier? Supplier { get; set; }
        public Employee? Employee { get; set; }
        public List<PurchaseItem> PurchaseItems { get; set; } = new();
        
        // Calculated properties
        public decimal CalculatedTotalAmount => PurchaseItems.Sum(pi => pi.TotalCost);
        public int TotalItems => PurchaseItems.Count;
        
        public string StatusDisplay => Status switch
        {
            PurchaseStatus.Pending => "في الانتظار",
            PurchaseStatus.Received => "تم الاستلام",
            PurchaseStatus.Cancelled => "ملغي",
            _ => "غير معروف"
        };
    }
}
