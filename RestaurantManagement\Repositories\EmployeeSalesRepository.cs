using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface IEmployeeSalesRepository
    {
        Task<IEnumerable<EmployeeSales>> GetAllAsync();
        Task<EmployeeSales?> GetByIdAsync(int id);
        Task<int> AddAsync(EmployeeSales entity);
        Task<bool> UpdateAsync(EmployeeSales entity);
        Task<bool> DeleteAsync(int id);
        Task<IEnumerable<EmployeeSales>> GetByEmployeeIdAsync(int employeeId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetEmployeeCommissionTotalAsync(int employeeId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<EmployeeSales>> GetSalesByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<bool> ExistsForOrderAsync(int employeeId, int orderId);
    }

    public class EmployeeSalesRepository : IEmployeeSalesRepository
    {
        private readonly DatabaseConnection _dbConnection;

        public EmployeeSalesRepository(DatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<IEnumerable<EmployeeSales>> GetAllAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT id, employee_id, order_id, sale_amount, commission_percentage, 
                              commission_amount, sale_date
                       FROM employee_sales 
                       ORDER BY sale_date DESC";
            return await connection.QueryAsync<EmployeeSales>(sql);
        }

        public async Task<EmployeeSales?> GetByIdAsync(int id)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT id, employee_id, order_id, sale_amount, commission_percentage, 
                              commission_amount, sale_date
                       FROM employee_sales 
                       WHERE id = @Id";
            return await connection.QueryFirstOrDefaultAsync<EmployeeSales>(sql, new { Id = id });
        }

        public async Task<int> AddAsync(EmployeeSales entity)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"INSERT INTO employee_sales (employee_id, order_id, sale_amount, commission_percentage, 
                                                   commission_amount, sale_date) 
                       VALUES (@EmployeeId, @OrderId, @SaleAmount, @CommissionPercentage, 
                              @CommissionAmount, @SaleDate);
                       SELECT LAST_INSERT_ID();";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public async Task<bool> UpdateAsync(EmployeeSales entity)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"UPDATE employee_sales SET 
                       sale_amount = @SaleAmount, 
                       commission_percentage = @CommissionPercentage,
                       commission_amount = @CommissionAmount,
                       sale_date = @SaleDate
                       WHERE id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, entity);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = "DELETE FROM employee_sales WHERE id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<EmployeeSales>> GetByEmployeeIdAsync(int employeeId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            using var connection = _dbConnection.CreateConnection();
            var whereClause = "WHERE es.employee_id = @EmployeeId";
            var parameters = new { EmployeeId = employeeId, FromDate = fromDate, ToDate = toDate };
            
            if (fromDate.HasValue)
                whereClause += " AND DATE(es.sale_date) >= DATE(@FromDate)";
            if (toDate.HasValue)
                whereClause += " AND DATE(es.sale_date) <= DATE(@ToDate)";
            
            var sql = @$"SELECT es.id, es.employee_id, es.order_id, es.sale_amount, es.commission_percentage, 
                               es.commission_amount, es.sale_date,
                               e.id, e.name, e.phone, e.email, e.role_id, e.hire_date, e.salary, 
                               e.commission_percentage, e.is_active, e.created_at, e.updated_at,
                               o.id, o.order_number, o.order_type, o.table_id, o.customer_id, 
                               o.employee_id, o.order_date, o.status, o.subtotal, o.tax_amount, 
                               o.total_amount, o.notes, o.delivery_address, o.delivery_phone
                        FROM employee_sales es
                        INNER JOIN employees e ON es.employee_id = e.id
                        INNER JOIN orders o ON es.order_id = o.id
                        {whereClause}
                        ORDER BY es.sale_date DESC";
            
            var sales = await connection.QueryAsync<EmployeeSales, Employee, Order, EmployeeSales>(
                sql,
                (sale, employee, order) =>
                {
                    sale.Employee = employee;
                    sale.Order = order;
                    return sale;
                },
                parameters,
                splitOn: "id,id"
            );
            
            return sales;
        }

        public async Task<decimal> GetEmployeeCommissionTotalAsync(int employeeId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            using var connection = _dbConnection.CreateConnection();
            var whereClause = "WHERE employee_id = @EmployeeId";
            var parameters = new { EmployeeId = employeeId, FromDate = fromDate, ToDate = toDate };
            
            if (fromDate.HasValue)
                whereClause += " AND DATE(sale_date) >= DATE(@FromDate)";
            if (toDate.HasValue)
                whereClause += " AND DATE(sale_date) <= DATE(@ToDate)";
            
            var sql = @$"SELECT COALESCE(SUM(commission_amount), 0) 
                        FROM employee_sales 
                        {whereClause}";
            
            return await connection.QuerySingleAsync<decimal>(sql, parameters);
        }

        public async Task<IEnumerable<EmployeeSales>> GetSalesByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT es.id, es.employee_id, es.order_id, es.sale_amount, es.commission_percentage, 
                              es.commission_amount, es.sale_date,
                              e.id, e.name, e.phone, e.email, e.role_id, e.hire_date, e.salary, 
                              e.commission_percentage, e.is_active, e.created_at, e.updated_at
                       FROM employee_sales es
                       INNER JOIN employees e ON es.employee_id = e.id
                       WHERE DATE(es.sale_date) >= DATE(@FromDate) 
                         AND DATE(es.sale_date) <= DATE(@ToDate)
                       ORDER BY es.sale_date DESC";
            
            var sales = await connection.QueryAsync<EmployeeSales, Employee, EmployeeSales>(
                sql,
                (sale, employee) =>
                {
                    sale.Employee = employee;
                    return sale;
                },
                new { FromDate = fromDate, ToDate = toDate },
                splitOn: "id"
            );
            
            return sales;
        }

        public async Task<bool> ExistsForOrderAsync(int employeeId, int orderId)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = "SELECT COUNT(1) FROM employee_sales WHERE employee_id = @EmployeeId AND order_id = @OrderId";
            var count = await connection.QuerySingleAsync<int>(sql, new { EmployeeId = employeeId, OrderId = orderId });
            return count > 0;
        }
    }
}
