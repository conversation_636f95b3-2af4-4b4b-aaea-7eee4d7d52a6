using RestaurantManagement.Models;
using RestaurantManagement.Repositories;

namespace RestaurantManagement.Services
{
    public interface IDishService
    {
        Task<IEnumerable<Dish>> GetAllDishesAsync();
        Task<Dish?> GetDishByIdAsync(int id);
        Task<Dish?> GetDishWithIngredientsAsync(int id);
        Task<int> CreateDishAsync(Dish dish);
        Task<bool> UpdateDishAsync(Dish dish);
        Task<bool> DeleteDishAsync(int id);
        Task<IEnumerable<Dish>> GetDishesByCategoryAsync(int categoryId);
        Task<bool> CalculateAndUpdateDishCostAsync(int dishId);
        Task<bool> AddIngredientToDishAsync(int dishId, int ingredientId, decimal quantity);
        Task<bool> RemoveIngredientFromDishAsync(int dishId, int ingredientId);
        Task<bool> UpdateDishIngredientAsync(int dishId, int ingredientId, decimal quantity);
    }

    public class DishService : IDishService
    {
        private readonly IDishRepository _dishRepository;
        private readonly IDishIngredientRepository _dishIngredientRepository;
        private readonly IIngredientRepository _ingredientRepository;

        public DishService(
            IDishRepository dishRepository,
            IDishIngredientRepository dishIngredientRepository,
            IIngredientRepository ingredientRepository)
        {
            _dishRepository = dishRepository;
            _dishIngredientRepository = dishIngredientRepository;
            _ingredientRepository = ingredientRepository;
        }

        public async Task<IEnumerable<Dish>> GetAllDishesAsync()
        {
            return await _dishRepository.GetDishesWithCategoryAsync();
        }

        public async Task<Dish?> GetDishByIdAsync(int id)
        {
            return await _dishRepository.GetByIdAsync(id);
        }

        public async Task<Dish?> GetDishWithIngredientsAsync(int id)
        {
            return await _dishRepository.GetDishWithIngredientsAsync(id);
        }

        public async Task<int> CreateDishAsync(Dish dish)
        {
            dish.CreatedAt = DateTime.Now;
            dish.UpdatedAt = DateTime.Now;
            return await _dishRepository.AddAsync(dish);
        }

        public async Task<bool> UpdateDishAsync(Dish dish)
        {
            dish.UpdatedAt = DateTime.Now;
            return await _dishRepository.UpdateAsync(dish);
        }

        public async Task<bool> DeleteDishAsync(int id)
        {
            return await _dishRepository.DeleteAsync(id);
        }

        public async Task<IEnumerable<Dish>> GetDishesByCategoryAsync(int categoryId)
        {
            return await _dishRepository.GetDishesByCategoryAsync(categoryId);
        }

        public async Task<bool> CalculateAndUpdateDishCostAsync(int dishId)
        {
            var dishIngredients = await _dishIngredientRepository.GetByDishIdAsync(dishId);
            var totalCost = dishIngredients.Sum(di => di.Cost);
            
            var dish = await _dishRepository.GetByIdAsync(dishId);
            if (dish == null) return false;
            
            var sellingPrice = totalCost * (1 + dish.ProfitMargin / 100);
            
            return await _dishRepository.UpdateDishCostAsync(dishId, totalCost, sellingPrice);
        }

        public async Task<bool> AddIngredientToDishAsync(int dishId, int ingredientId, decimal quantity)
        {
            // Check if ingredient already exists for this dish
            if (await _dishIngredientRepository.ExistsAsync(dishId, ingredientId))
                return false;

            var ingredient = await _ingredientRepository.GetByIdAsync(ingredientId);
            if (ingredient == null) return false;

            var dishIngredient = new DishIngredient
            {
                DishId = dishId,
                IngredientId = ingredientId,
                Quantity = quantity,
                Cost = quantity * ingredient.CostPerUnit
            };

            var result = await _dishIngredientRepository.AddAsync(dishIngredient);
            if (result > 0)
            {
                await CalculateAndUpdateDishCostAsync(dishId);
                return true;
            }
            return false;
        }

        public async Task<bool> RemoveIngredientFromDishAsync(int dishId, int ingredientId)
        {
            var dishIngredients = await _dishIngredientRepository.GetByDishIdAsync(dishId);
            var dishIngredient = dishIngredients.FirstOrDefault(di => di.IngredientId == ingredientId);
            
            if (dishIngredient == null) return false;

            var result = await _dishIngredientRepository.DeleteAsync(dishIngredient.Id);
            if (result)
            {
                await CalculateAndUpdateDishCostAsync(dishId);
                return true;
            }
            return false;
        }

        public async Task<bool> UpdateDishIngredientAsync(int dishId, int ingredientId, decimal quantity)
        {
            var dishIngredients = await _dishIngredientRepository.GetByDishIdAsync(dishId);
            var dishIngredient = dishIngredients.FirstOrDefault(di => di.IngredientId == ingredientId);
            
            if (dishIngredient == null) return false;

            var ingredient = await _ingredientRepository.GetByIdAsync(ingredientId);
            if (ingredient == null) return false;

            dishIngredient.Quantity = quantity;
            dishIngredient.Cost = quantity * ingredient.CostPerUnit;

            var result = await _dishIngredientRepository.UpdateAsync(dishIngredient);
            if (result)
            {
                await CalculateAndUpdateDishCostAsync(dishId);
                return true;
            }
            return false;
        }
    }
}
