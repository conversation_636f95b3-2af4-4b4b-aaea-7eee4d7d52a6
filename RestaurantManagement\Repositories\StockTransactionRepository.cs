using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface IStockTransactionRepository
    {
        Task<IEnumerable<StockTransaction>> GetAllAsync();
        Task<StockTransaction?> GetByIdAsync(int id);
        Task<int> AddAsync(StockTransaction entity);
        Task<IEnumerable<StockTransaction>> GetByIngredientIdAsync(int ingredientId);
        Task<IEnumerable<StockTransaction>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<StockTransaction>> GetByTransactionTypeAsync(TransactionType transactionType);
        Task<IEnumerable<StockTransaction>> GetTransactionsWithDetailsAsync();
    }

    public class StockTransactionRepository : IStockTransactionRepository
    {
        private readonly DatabaseConnection _dbConnection;

        public StockTransactionRepository(DatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<IEnumerable<StockTransaction>> GetAllAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT id, ingredient_id, transaction_type, quantity, unit_cost, total_cost, 
                              reference_type, reference_id, notes, employee_id, transaction_date
                       FROM stock_transactions 
                       ORDER BY transaction_date DESC";
            return await connection.QueryAsync<StockTransaction>(sql);
        }

        public async Task<StockTransaction?> GetByIdAsync(int id)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT id, ingredient_id, transaction_type, quantity, unit_cost, total_cost, 
                              reference_type, reference_id, notes, employee_id, transaction_date
                       FROM stock_transactions 
                       WHERE id = @Id";
            return await connection.QueryFirstOrDefaultAsync<StockTransaction>(sql, new { Id = id });
        }

        public async Task<int> AddAsync(StockTransaction entity)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"INSERT INTO stock_transactions (ingredient_id, transaction_type, quantity, unit_cost, 
                                                       total_cost, reference_type, reference_id, notes, 
                                                       employee_id, transaction_date) 
                       VALUES (@IngredientId, @TransactionType, @Quantity, @UnitCost, @TotalCost, 
                              @ReferenceType, @ReferenceId, @Notes, @EmployeeId, @TransactionDate);
                       SELECT LAST_INSERT_ID();";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public async Task<IEnumerable<StockTransaction>> GetByIngredientIdAsync(int ingredientId)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT st.id, st.ingredient_id, st.transaction_type, st.quantity, st.unit_cost, 
                              st.total_cost, st.reference_type, st.reference_id, st.notes, st.employee_id, 
                              st.transaction_date,
                              i.id, i.name, i.unit_id, i.cost_per_unit, i.current_stock, i.minimum_stock,
                              i.is_active, i.created_at, i.updated_at,
                              e.id, e.name, e.phone, e.email, e.role_id, e.hire_date, e.salary, 
                              e.commission_percentage, e.is_active, e.created_at, e.updated_at
                       FROM stock_transactions st
                       INNER JOIN ingredients i ON st.ingredient_id = i.id
                       INNER JOIN employees e ON st.employee_id = e.id
                       WHERE st.ingredient_id = @IngredientId
                       ORDER BY st.transaction_date DESC";
            
            var transactions = await connection.QueryAsync<StockTransaction, Ingredient, Employee, StockTransaction>(
                sql,
                (transaction, ingredient, employee) =>
                {
                    transaction.Ingredient = ingredient;
                    transaction.Employee = employee;
                    return transaction;
                },
                new { IngredientId = ingredientId },
                splitOn: "id,id"
            );
            
            return transactions;
        }

        public async Task<IEnumerable<StockTransaction>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT st.id, st.ingredient_id, st.transaction_type, st.quantity, st.unit_cost, 
                              st.total_cost, st.reference_type, st.reference_id, st.notes, st.employee_id, 
                              st.transaction_date,
                              i.id, i.name, i.unit_id, i.cost_per_unit, i.current_stock, i.minimum_stock,
                              i.is_active, i.created_at, i.updated_at,
                              e.id, e.name, e.phone, e.email, e.role_id, e.hire_date, e.salary, 
                              e.commission_percentage, e.is_active, e.created_at, e.updated_at
                       FROM stock_transactions st
                       INNER JOIN ingredients i ON st.ingredient_id = i.id
                       INNER JOIN employees e ON st.employee_id = e.id
                       WHERE DATE(st.transaction_date) >= DATE(@FromDate) 
                         AND DATE(st.transaction_date) <= DATE(@ToDate)
                       ORDER BY st.transaction_date DESC";
            
            var transactions = await connection.QueryAsync<StockTransaction, Ingredient, Employee, StockTransaction>(
                sql,
                (transaction, ingredient, employee) =>
                {
                    transaction.Ingredient = ingredient;
                    transaction.Employee = employee;
                    return transaction;
                },
                new { FromDate = fromDate, ToDate = toDate },
                splitOn: "id,id"
            );
            
            return transactions;
        }

        public async Task<IEnumerable<StockTransaction>> GetByTransactionTypeAsync(TransactionType transactionType)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT st.id, st.ingredient_id, st.transaction_type, st.quantity, st.unit_cost, 
                              st.total_cost, st.reference_type, st.reference_id, st.notes, st.employee_id, 
                              st.transaction_date,
                              i.id, i.name, i.unit_id, i.cost_per_unit, i.current_stock, i.minimum_stock,
                              i.is_active, i.created_at, i.updated_at
                       FROM stock_transactions st
                       INNER JOIN ingredients i ON st.ingredient_id = i.id
                       WHERE st.transaction_type = @TransactionType
                       ORDER BY st.transaction_date DESC";
            
            var transactions = await connection.QueryAsync<StockTransaction, Ingredient, StockTransaction>(
                sql,
                (transaction, ingredient) =>
                {
                    transaction.Ingredient = ingredient;
                    return transaction;
                },
                new { TransactionType = transactionType },
                splitOn: "id"
            );
            
            return transactions;
        }

        public async Task<IEnumerable<StockTransaction>> GetTransactionsWithDetailsAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT st.id, st.ingredient_id, st.transaction_type, st.quantity, st.unit_cost, 
                              st.total_cost, st.reference_type, st.reference_id, st.notes, st.employee_id, 
                              st.transaction_date,
                              i.id, i.name, i.unit_id, i.cost_per_unit, i.current_stock, i.minimum_stock,
                              i.is_active, i.created_at, i.updated_at,
                              e.id, e.name, e.phone, e.email, e.role_id, e.hire_date, e.salary, 
                              e.commission_percentage, e.is_active, e.created_at, e.updated_at
                       FROM stock_transactions st
                       INNER JOIN ingredients i ON st.ingredient_id = i.id
                       INNER JOIN employees e ON st.employee_id = e.id
                       ORDER BY st.transaction_date DESC";
            
            var transactions = await connection.QueryAsync<StockTransaction, Ingredient, Employee, StockTransaction>(
                sql,
                (transaction, ingredient, employee) =>
                {
                    transaction.Ingredient = ingredient;
                    transaction.Employee = employee;
                    return transaction;
                },
                splitOn: "id,id"
            );
            
            return transactions;
        }
    }
}
