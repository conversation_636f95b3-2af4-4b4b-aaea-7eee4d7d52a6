using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface IEmployeeRepository : IRepository<Employee>
    {
        Task<IEnumerable<Employee>> GetEmployeesWithRolesAsync();
        Task<IEnumerable<Employee>> GetActiveEmployeesAsync();
        Task<IEnumerable<Employee>> GetEmployeesByRoleAsync(int roleId);
        Task<IEnumerable<Employee>> GetEmployeesWithCommissionAsync();
    }

    public class EmployeeRepository : BaseRepository<Employee>, IEmployeeRepository
    {
        protected override string TableName => "employees";
        protected override string SelectColumns => @"e.id, e.name, e.phone, e.email, e.role_id, e.hire_date, 
                                                    e.salary, e.commission_percentage, e.is_active, 
                                                    e.created_at, e.updated_at";

        public EmployeeRepository(DatabaseConnection dbConnection) : base(dbConnection) { }

        public override async Task<int> AddAsync(Employee entity)
        {
            var sql = @"INSERT INTO employees (name, phone, email, role_id, hire_date, salary, 
                                             commission_percentage, is_active, created_at, updated_at) 
                       VALUES (@Name, @Phone, @Email, @RoleId, @HireDate, @Salary, 
                              @CommissionPercentage, @IsActive, @CreatedAt, @UpdatedAt)";
            return await ExecuteInsertAsync(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Employee entity)
        {
            entity.UpdatedAt = DateTime.Now;
            var sql = @"UPDATE employees SET 
                       name = @Name, 
                       phone = @Phone,
                       email = @Email,
                       role_id = @RoleId,
                       hire_date = @HireDate,
                       salary = @Salary,
                       commission_percentage = @CommissionPercentage,
                       is_active = @IsActive,
                       updated_at = @UpdatedAt 
                       WHERE id = @Id";
            return await ExecuteUpdateAsync(sql, entity);
        }

        public async Task<IEnumerable<Employee>> GetEmployeesWithRolesAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns}, r.id, r.name, r.permissions, r.is_active
                        FROM employees e
                        INNER JOIN employee_roles r ON e.role_id = r.id
                        WHERE e.is_active = 1
                        ORDER BY e.name";
            
            var employees = await connection.QueryAsync<Employee, EmployeeRole, Employee>(
                sql,
                (employee, role) =>
                {
                    employee.Role = role;
                    return employee;
                },
                splitOn: "id"
            );
            
            return employees;
        }

        public async Task<IEnumerable<Employee>> GetActiveEmployeesAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns}, r.id, r.name, r.permissions, r.is_active
                        FROM employees e
                        INNER JOIN employee_roles r ON e.role_id = r.id
                        WHERE e.is_active = 1
                        ORDER BY e.name";
            
            var employees = await connection.QueryAsync<Employee, EmployeeRole, Employee>(
                sql,
                (employee, role) =>
                {
                    employee.Role = role;
                    return employee;
                },
                splitOn: "id"
            );
            
            return employees;
        }

        public async Task<IEnumerable<Employee>> GetEmployeesByRoleAsync(int roleId)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns}, r.id, r.name, r.permissions, r.is_active
                        FROM employees e
                        INNER JOIN employee_roles r ON e.role_id = r.id
                        WHERE e.role_id = @RoleId AND e.is_active = 1
                        ORDER BY e.name";
            
            var employees = await connection.QueryAsync<Employee, EmployeeRole, Employee>(
                sql,
                (employee, role) =>
                {
                    employee.Role = role;
                    return employee;
                },
                new { RoleId = roleId },
                splitOn: "id"
            );
            
            return employees;
        }

        public async Task<IEnumerable<Employee>> GetEmployeesWithCommissionAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns}, r.id, r.name, r.permissions, r.is_active
                        FROM employees e
                        INNER JOIN employee_roles r ON e.role_id = r.id
                        WHERE e.commission_percentage > 0 AND e.is_active = 1
                        ORDER BY e.name";
            
            var employees = await connection.QueryAsync<Employee, EmployeeRole, Employee>(
                sql,
                (employee, role) =>
                {
                    employee.Role = role;
                    return employee;
                },
                splitOn: "id"
            );
            
            return employees;
        }
    }
}
