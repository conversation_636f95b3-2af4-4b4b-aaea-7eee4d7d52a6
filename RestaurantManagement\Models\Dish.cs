using System.Collections.Generic;

namespace RestaurantManagement.Models
{
    public class Dish : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public int CategoryId { get; set; }
        public string? Description { get; set; }
        public decimal CostPrice { get; set; }
        public decimal ProfitMargin { get; set; }
        public decimal SellingPrice { get; set; }
        public int PreparationTime { get; set; } // in minutes
        
        // Navigation properties
        public Category? Category { get; set; }
        public List<DishIngredient> DishIngredients { get; set; } = new();
        
        // Calculated properties
        public decimal CalculatedCostPrice => DishIngredients.Sum(di => di.Cost);
        public decimal CalculatedSellingPrice => CalculatedCostPrice * (1 + ProfitMargin / 100);
        public decimal ProfitAmount => SellingPrice - CostPrice;
        public decimal ActualProfitMargin => CostPrice > 0 ? ((SellingPrice - CostPrice) / CostPrice) * 100 : 0;
    }
}
