using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface IUnitRepository : IRepository<Unit>
    {
        Task<IEnumerable<Unit>> GetActiveUnitsAsync();
    }

    public class UnitRepository : BaseRepository<Unit>, IUnitRepository
    {
        protected override string TableName => "units";
        protected override string SelectColumns => "id, name, abbreviation, is_active";

        public UnitRepository(DatabaseConnection dbConnection) : base(dbConnection) { }

        public override async Task<int> AddAsync(Unit entity)
        {
            var sql = @"INSERT INTO units (name, abbreviation, is_active) 
                       VALUES (@Name, @Abbreviation, @IsActive)";
            return await ExecuteInsertAsync(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Unit entity)
        {
            var sql = @"UPDATE units SET 
                       name = @Name, 
                       abbreviation = @Abbreviation, 
                       is_active = @IsActive 
                       WHERE id = @Id";
            return await ExecuteUpdateAsync(sql, entity);
        }

        public async Task<IEnumerable<Unit>> GetActiveUnitsAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"SELECT {SelectColumns} FROM {TableName} WHERE is_active = 1 ORDER BY name";
            return await connection.QueryAsync<Unit>(sql);
        }
    }
}
