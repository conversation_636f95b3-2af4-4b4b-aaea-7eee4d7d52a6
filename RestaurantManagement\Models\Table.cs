namespace RestaurantManagement.Models
{
    public enum TableStatus
    {
        Available,
        Occupied,
        Reserved,
        OutOfOrder
    }

    public class Table
    {
        public int Id { get; set; }
        public string TableNumber { get; set; } = string.Empty;
        public int Capacity { get; set; }
        public TableStatus Status { get; set; } = TableStatus.Available;
        public bool IsActive { get; set; } = true;
        
        public string StatusDisplay => Status switch
        {
            TableStatus.Available => "متاحة",
            TableStatus.Occupied => "مشغولة",
            TableStatus.Reserved => "محجوزة",
            TableStatus.OutOfOrder => "خارج الخدمة",
            _ => "غير معروف"
        };
        
        public bool CanTakeOrder => Status == TableStatus.Available && IsActive;
    }
}
