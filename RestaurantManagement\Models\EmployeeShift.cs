using System;

namespace RestaurantManagement.Models
{
    public enum ShiftStatus
    {
        Scheduled,
        CheckedIn,
        CheckedOut,
        Absent
    }

    public class EmployeeShift
    {
        public int Id { get; set; }
        public int EmployeeId { get; set; }
        public int ShiftId { get; set; }
        public DateTime WorkDate { get; set; }
        public DateTime? CheckInTime { get; set; }
        public DateTime? CheckOutTime { get; set; }
        public ShiftStatus Status { get; set; } = ShiftStatus.Scheduled;
        
        // Navigation properties
        public Employee? Employee { get; set; }
        public Shift? Shift { get; set; }
        
        // Calculated properties
        public TimeSpan? WorkedHours => CheckOutTime.HasValue && CheckInTime.HasValue 
            ? CheckOutTime.Value - CheckInTime.Value 
            : null;
        
        public bool IsLate => CheckInTime.HasValue && Shift != null 
            && CheckInTime.Value.TimeOfDay > Shift.StartTime.Add(TimeSpan.FromMinutes(15));
        
        public bool IsEarlyCheckout => CheckOutTime.HasValue && Shift != null 
            && CheckOutTime.Value.TimeOfDay < Shift.EndTime.Subtract(TimeSpan.FromMinutes(15));
    }
}
