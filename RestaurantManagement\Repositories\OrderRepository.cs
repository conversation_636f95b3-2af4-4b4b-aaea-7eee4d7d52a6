using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface IOrderRepository : IRepository<Order>
    {
        Task<IEnumerable<Order>> GetOrdersWithDetailsAsync();
        Task<Order?> GetOrderWithItemsAsync(int orderId);
        Task<IEnumerable<Order>> GetOrdersByStatusAsync(OrderStatus status);
        Task<IEnumerable<Order>> GetOrdersByEmployeeAsync(int employeeId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<Order>> GetOrdersByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<bool> UpdateOrderStatusAsync(int orderId, OrderStatus status);
        Task<string> GenerateOrderNumberAsync();
        Task<IEnumerable<Order>> GetKitchenOrdersAsync();
    }

    public class OrderRepository : BaseRepository<Order>, IOrderRepository
    {
        protected override string TableName => "orders";
        protected override string SelectColumns => @"o.id, o.order_number, o.order_type, o.table_id, o.customer_id, 
                                                    o.employee_id, o.order_date, o.status, o.subtotal, o.tax_amount, 
                                                    o.total_amount, o.notes, o.delivery_address, o.delivery_phone";

        public OrderRepository(DatabaseConnection dbConnection) : base(dbConnection) { }

        public override async Task<IEnumerable<Order>> GetAllAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"SELECT {SelectColumns} FROM {TableName} ORDER BY order_date DESC";
            return await connection.QueryAsync<Order>(sql);
        }

        public override async Task<int> AddAsync(Order entity)
        {
            var sql = @"INSERT INTO orders (order_number, order_type, table_id, customer_id, employee_id, 
                                          order_date, status, subtotal, tax_amount, total_amount, notes, 
                                          delivery_address, delivery_phone) 
                       VALUES (@OrderNumber, @OrderType, @TableId, @CustomerId, @EmployeeId, 
                              @OrderDate, @Status, @Subtotal, @TaxAmount, @TotalAmount, @Notes, 
                              @DeliveryAddress, @DeliveryPhone)";
            return await ExecuteInsertAsync(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Order entity)
        {
            var sql = @"UPDATE orders SET 
                       order_type = @OrderType,
                       table_id = @TableId,
                       customer_id = @CustomerId,
                       employee_id = @EmployeeId,
                       status = @Status,
                       subtotal = @Subtotal,
                       tax_amount = @TaxAmount,
                       total_amount = @TotalAmount,
                       notes = @Notes,
                       delivery_address = @DeliveryAddress,
                       delivery_phone = @DeliveryPhone
                       WHERE id = @Id";
            return await ExecuteUpdateAsync(sql, entity);
        }

        public override async Task<bool> DeleteAsync(int id)
        {
            // For orders, we don't soft delete, we change status to cancelled
            return await UpdateOrderStatusAsync(id, OrderStatus.Cancelled);
        }

        public async Task<IEnumerable<Order>> GetOrdersWithDetailsAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns}, 
                               t.id, t.table_number, t.capacity, t.status, t.is_active,
                               c.id, c.name, c.phone, c.address, c.email, c.is_active, c.created_at, c.updated_at,
                               e.id, e.name, e.phone, e.email, e.role_id, e.hire_date, e.salary, 
                               e.commission_percentage, e.is_active, e.created_at, e.updated_at
                        FROM orders o
                        LEFT JOIN tables t ON o.table_id = t.id
                        LEFT JOIN customers c ON o.customer_id = c.id
                        INNER JOIN employees e ON o.employee_id = e.id
                        ORDER BY o.order_date DESC";
            
            var orders = await connection.QueryAsync<Order, Table, Customer, Employee, Order>(
                sql,
                (order, table, customer, employee) =>
                {
                    order.Table = table;
                    order.Customer = customer;
                    order.Employee = employee;
                    return order;
                },
                splitOn: "id,id,id"
            );
            
            return orders;
        }

        public async Task<Order?> GetOrderWithItemsAsync(int orderId)
        {
            using var connection = _dbConnection.CreateConnection();
            
            // Get order with related data
            var orderSql = @$"SELECT {SelectColumns}, 
                                    t.id, t.table_number, t.capacity, t.status, t.is_active,
                                    c.id, c.name, c.phone, c.address, c.email, c.is_active, c.created_at, c.updated_at,
                                    e.id, e.name, e.phone, e.email, e.role_id, e.hire_date, e.salary, 
                                    e.commission_percentage, e.is_active, e.created_at, e.updated_at
                             FROM orders o
                             LEFT JOIN tables t ON o.table_id = t.id
                             LEFT JOIN customers c ON o.customer_id = c.id
                             INNER JOIN employees e ON o.employee_id = e.id
                             WHERE o.id = @OrderId";
            
            var orders = await connection.QueryAsync<Order, Table, Customer, Employee, Order>(
                orderSql,
                (order, table, customer, employee) =>
                {
                    order.Table = table;
                    order.Customer = customer;
                    order.Employee = employee;
                    return order;
                },
                new { OrderId = orderId },
                splitOn: "id,id,id"
            );
            
            var result = orders.FirstOrDefault();
            if (result == null) return null;

            // Get order items
            var itemsSql = @"SELECT oi.id, oi.order_id, oi.dish_id, oi.quantity, oi.unit_price, 
                                   oi.total_price, oi.special_instructions,
                                   d.id, d.name, d.category_id, d.description, d.cost_price, 
                                   d.profit_margin, d.selling_price, d.preparation_time, 
                                   d.is_active, d.created_at, d.updated_at
                            FROM order_items oi
                            INNER JOIN dishes d ON oi.dish_id = d.id
                            WHERE oi.order_id = @OrderId";
            
            var orderItems = await connection.QueryAsync<OrderItem, Dish, OrderItem>(
                itemsSql,
                (orderItem, dish) =>
                {
                    orderItem.Dish = dish;
                    return orderItem;
                },
                new { OrderId = orderId },
                splitOn: "id"
            );
            
            result.OrderItems = orderItems.ToList();
            return result;
        }

        public async Task<IEnumerable<Order>> GetOrdersByStatusAsync(OrderStatus status)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns}, 
                               t.id, t.table_number, t.capacity, t.status, t.is_active,
                               c.id, c.name, c.phone, c.address, c.email, c.is_active, c.created_at, c.updated_at,
                               e.id, e.name, e.phone, e.email, e.role_id, e.hire_date, e.salary, 
                               e.commission_percentage, e.is_active, e.created_at, e.updated_at
                        FROM orders o
                        LEFT JOIN tables t ON o.table_id = t.id
                        LEFT JOIN customers c ON o.customer_id = c.id
                        INNER JOIN employees e ON o.employee_id = e.id
                        WHERE o.status = @Status
                        ORDER BY o.order_date DESC";
            
            var orders = await connection.QueryAsync<Order, Table, Customer, Employee, Order>(
                sql,
                (order, table, customer, employee) =>
                {
                    order.Table = table;
                    order.Customer = customer;
                    order.Employee = employee;
                    return order;
                },
                new { Status = status },
                splitOn: "id,id,id"
            );
            
            return orders;
        }

        public async Task<IEnumerable<Order>> GetOrdersByEmployeeAsync(int employeeId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            using var connection = _dbConnection.CreateConnection();
            var whereClause = "WHERE o.employee_id = @EmployeeId";
            var parameters = new { EmployeeId = employeeId, FromDate = fromDate, ToDate = toDate };
            
            if (fromDate.HasValue)
                whereClause += " AND DATE(o.order_date) >= DATE(@FromDate)";
            if (toDate.HasValue)
                whereClause += " AND DATE(o.order_date) <= DATE(@ToDate)";
            
            var sql = @$"SELECT {SelectColumns}
                        FROM orders o
                        {whereClause}
                        ORDER BY o.order_date DESC";
            
            return await connection.QueryAsync<Order>(sql, parameters);
        }

        public async Task<IEnumerable<Order>> GetOrdersByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns}
                        FROM orders o
                        WHERE DATE(o.order_date) >= DATE(@FromDate) AND DATE(o.order_date) <= DATE(@ToDate)
                        ORDER BY o.order_date DESC";
            
            return await connection.QueryAsync<Order>(sql, new { FromDate = fromDate, ToDate = toDate });
        }

        public async Task<bool> UpdateOrderStatusAsync(int orderId, OrderStatus status)
        {
            var sql = "UPDATE orders SET status = @Status WHERE id = @Id";
            return await ExecuteUpdateAsync(sql, new { Id = orderId, Status = status });
        }

        public async Task<string> GenerateOrderNumberAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var today = DateTime.Today.ToString("yyyyMMdd");
            var sql = @"SELECT COUNT(*) FROM orders WHERE DATE(order_date) = DATE(@Today)";
            var count = await connection.QuerySingleAsync<int>(sql, new { Today = DateTime.Today });
            return $"ORD-{today}-{(count + 1):D4}";
        }

        public async Task<IEnumerable<Order>> GetKitchenOrdersAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns}, 
                               t.id, t.table_number, t.capacity, t.status, t.is_active,
                               c.id, c.name, c.phone, c.address, c.email, c.is_active, c.created_at, c.updated_at
                        FROM orders o
                        LEFT JOIN tables t ON o.table_id = t.id
                        LEFT JOIN customers c ON o.customer_id = c.id
                        WHERE o.status IN ('New', 'Preparing')
                        ORDER BY o.order_date ASC";
            
            var orders = await connection.QueryAsync<Order, Table, Customer, Order>(
                sql,
                (order, table, customer) =>
                {
                    order.Table = table;
                    order.Customer = customer;
                    return order;
                },
                splitOn: "id,id"
            );
            
            return orders;
        }
    }
}
