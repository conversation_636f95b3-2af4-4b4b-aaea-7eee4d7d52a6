using Dapper;
using RestaurantManagement.Data;
using System.Data;

namespace RestaurantManagement.Repositories
{
    public abstract class BaseRepository<T> : IRepository<T> where T : class
    {
        protected readonly DatabaseConnection _dbConnection;
        protected abstract string TableName { get; }
        protected abstract string SelectColumns { get; }

        protected BaseRepository(DatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"SELECT {SelectColumns} FROM {TableName} WHERE is_active = 1";
            return await connection.QueryAsync<T>(sql);
        }

        public virtual async Task<T?> GetByIdAsync(int id)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"SELECT {SelectColumns} FROM {TableName} WHERE id = @Id";
            return await connection.QueryFirstOrDefaultAsync<T>(sql, new { Id = id });
        }

        public abstract Task<int> AddAsync(T entity);
        public abstract Task<bool> UpdateAsync(T entity);

        public virtual async Task<bool> DeleteAsync(int id)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"UPDATE {TableName} SET is_active = 0 WHERE id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public virtual async Task<bool> ExistsAsync(int id)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"SELECT COUNT(1) FROM {TableName} WHERE id = @Id";
            var count = await connection.QuerySingleAsync<int>(sql, new { Id = id });
            return count > 0;
        }

        protected async Task<int> ExecuteInsertAsync(string sql, object parameters)
        {
            using var connection = _dbConnection.CreateConnection();
            sql += "; SELECT LAST_INSERT_ID();";
            return await connection.QuerySingleAsync<int>(sql, parameters);
        }

        protected async Task<bool> ExecuteUpdateAsync(string sql, object parameters)
        {
            using var connection = _dbConnection.CreateConnection();
            var rowsAffected = await connection.ExecuteAsync(sql, parameters);
            return rowsAffected > 0;
        }
    }
}
