using System;

namespace RestaurantManagement.Models
{
    public enum TransactionType
    {
        Purchase,
        Usage,
        Adjustment,
        Waste
    }

    public enum ReferenceType
    {
        Order,
        Purchase,
        Manual
    }

    public class StockTransaction
    {
        public int Id { get; set; }
        public int IngredientId { get; set; }
        public TransactionType TransactionType { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public decimal TotalCost { get; set; }
        public ReferenceType ReferenceType { get; set; } = ReferenceType.Manual;
        public int? ReferenceId { get; set; }
        public string? Notes { get; set; }
        public int EmployeeId { get; set; }
        public DateTime TransactionDate { get; set; } = DateTime.Now;
        
        // Navigation properties
        public Ingredient? Ingredient { get; set; }
        public Employee? Employee { get; set; }
        
        public string TransactionTypeDisplay => TransactionType switch
        {
            TransactionType.Purchase => "شراء",
            TransactionType.Usage => "استخدام",
            TransactionType.Adjustment => "تعديل",
            TransactionType.Waste => "تالف",
            _ => "غير معروف"
        };
        
        public string ReferenceTypeDisplay => ReferenceType switch
        {
            ReferenceType.Order => "طلب",
            ReferenceType.Purchase => "مشتريات",
            ReferenceType.Manual => "يدوي",
            _ => "غير معروف"
        };
    }
}
