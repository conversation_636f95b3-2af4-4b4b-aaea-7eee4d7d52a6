using System;

namespace RestaurantManagement.Models
{
    public class Shift
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public bool IsActive { get; set; } = true;
        
        public string DisplayTime => $"{StartTime:hh\\:mm} - {EndTime:hh\\:mm}";
        public bool IsCurrentShift
        {
            get
            {
                var now = DateTime.Now.TimeOfDay;
                if (StartTime <= EndTime)
                    return now >= StartTime && now <= EndTime;
                else // Night shift crossing midnight
                    return now >= StartTime || now <= EndTime;
            }
        }
    }
}
