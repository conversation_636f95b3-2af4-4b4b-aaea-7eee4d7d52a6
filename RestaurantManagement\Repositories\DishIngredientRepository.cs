using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface IDishIngredientRepository
    {
        Task<IEnumerable<DishIngredient>> GetByDishIdAsync(int dishId);
        Task<int> AddAsync(DishIngredient entity);
        Task<bool> UpdateAsync(DishIngredient entity);
        Task<bool> DeleteAsync(int id);
        Task<bool> DeleteByDishIdAsync(int dishId);
        Task<bool> ExistsAsync(int dishId, int ingredientId);
    }

    public class DishIngredientRepository : IDishIngredientRepository
    {
        private readonly DatabaseConnection _dbConnection;

        public DishIngredientRepository(DatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<IEnumerable<DishIngredient>> GetByDishIdAsync(int dishId)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT di.id, di.dish_id, di.ingredient_id, di.quantity, di.cost,
                              i.id, i.name, i.unit_id, i.cost_per_unit, i.current_stock, i.minimum_stock,
                              i.is_active, i.created_at, i.updated_at,
                              u.id, u.name, u.abbreviation, u.is_active
                       FROM dish_ingredients di
                       INNER JOIN ingredients i ON di.ingredient_id = i.id
                       INNER JOIN units u ON i.unit_id = u.id
                       WHERE di.dish_id = @DishId";
            
            var dishIngredients = await connection.QueryAsync<DishIngredient, Ingredient, Unit, DishIngredient>(
                sql,
                (dishIngredient, ingredient, unit) =>
                {
                    ingredient.Unit = unit;
                    dishIngredient.Ingredient = ingredient;
                    return dishIngredient;
                },
                new { DishId = dishId },
                splitOn: "id,id"
            );
            
            return dishIngredients;
        }

        public async Task<int> AddAsync(DishIngredient entity)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"INSERT INTO dish_ingredients (dish_id, ingredient_id, quantity, cost) 
                       VALUES (@DishId, @IngredientId, @Quantity, @Cost);
                       SELECT LAST_INSERT_ID();";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public async Task<bool> UpdateAsync(DishIngredient entity)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"UPDATE dish_ingredients SET 
                       quantity = @Quantity, 
                       cost = @Cost 
                       WHERE id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, entity);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = "DELETE FROM dish_ingredients WHERE id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteByDishIdAsync(int dishId)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = "DELETE FROM dish_ingredients WHERE dish_id = @DishId";
            var rowsAffected = await connection.ExecuteAsync(sql, new { DishId = dishId });
            return rowsAffected > 0;
        }

        public async Task<bool> ExistsAsync(int dishId, int ingredientId)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = "SELECT COUNT(1) FROM dish_ingredients WHERE dish_id = @DishId AND ingredient_id = @IngredientId";
            var count = await connection.QuerySingleAsync<int>(sql, new { DishId = dishId, IngredientId = ingredientId });
            return count > 0;
        }
    }
}
