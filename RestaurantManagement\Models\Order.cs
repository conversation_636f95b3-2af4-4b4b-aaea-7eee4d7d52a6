using System;
using System.Collections.Generic;

namespace RestaurantManagement.Models
{
    public enum OrderType
    {
        DineIn,
        TakeAway,
        Delivery
    }

    public enum OrderStatus
    {
        New,
        Preparing,
        Ready,
        Delivered,
        Cancelled
    }

    public class Order
    {
        public int Id { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public OrderType OrderType { get; set; }
        public int? TableId { get; set; }
        public int? CustomerId { get; set; }
        public int EmployeeId { get; set; }
        public DateTime OrderDate { get; set; } = DateTime.Now;
        public OrderStatus Status { get; set; } = OrderStatus.New;
        public decimal Subtotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public string? Notes { get; set; }
        public string? DeliveryAddress { get; set; }
        public string? DeliveryPhone { get; set; }
        
        // Navigation properties
        public Table? Table { get; set; }
        public Customer? Customer { get; set; }
        public Employee? Employee { get; set; }
        public List<OrderItem> OrderItems { get; set; } = new();
        
        // Calculated properties
        public decimal CalculatedSubtotal => OrderItems.Sum(oi => oi.TotalPrice);
        public int TotalItems => OrderItems.Sum(oi => oi.Quantity);
        
        public string OrderTypeDisplay => OrderType switch
        {
            OrderType.DineIn => "طلب داخلي",
            OrderType.TakeAway => "طلب خارجي",
            OrderType.Delivery => "توصيل",
            _ => "غير معروف"
        };
        
        public string StatusDisplay => Status switch
        {
            OrderStatus.New => "جديد",
            OrderStatus.Preparing => "تحت التحضير",
            OrderStatus.Ready => "جاهز",
            OrderStatus.Delivered => "تم التسليم",
            OrderStatus.Cancelled => "ملغي",
            _ => "غير معروف"
        };
        
        public string DisplayInfo => OrderType == OrderType.DineIn && Table != null 
            ? $"طاولة {Table.TableNumber}" 
            : OrderType == OrderType.Delivery && Customer != null 
                ? Customer.Name 
                : "طلب خارجي";
    }
}
