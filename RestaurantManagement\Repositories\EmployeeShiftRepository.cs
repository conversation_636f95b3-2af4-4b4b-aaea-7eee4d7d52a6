using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface IEmployeeShiftRepository
    {
        Task<IEnumerable<EmployeeShift>> GetAllAsync();
        Task<EmployeeShift?> GetByIdAsync(int id);
        Task<int> AddAsync(EmployeeShift entity);
        Task<bool> UpdateAsync(EmployeeShift entity);
        Task<bool> DeleteAsync(int id);
        Task<IEnumerable<EmployeeShift>> GetByEmployeeIdAsync(int employeeId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<EmployeeShift>> GetByDateAsync(DateTime date);
        Task<IEnumerable<EmployeeShift>> GetCurrentWorkingEmployeesAsync();
        Task<bool> CheckInAsync(int employeeShiftId);
        Task<bool> CheckOutAsync(int employeeShiftId);
        Task<EmployeeShift?> GetActiveShiftForEmployeeAsync(int employeeId, DateTime date);
    }

    public class EmployeeShiftRepository : IEmployeeShiftRepository
    {
        private readonly DatabaseConnection _dbConnection;

        public EmployeeShiftRepository(DatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<IEnumerable<EmployeeShift>> GetAllAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT id, employee_id, shift_id, work_date, check_in_time, check_out_time, status
                       FROM employee_shifts 
                       ORDER BY work_date DESC, check_in_time DESC";
            return await connection.QueryAsync<EmployeeShift>(sql);
        }

        public async Task<EmployeeShift?> GetByIdAsync(int id)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT id, employee_id, shift_id, work_date, check_in_time, check_out_time, status
                       FROM employee_shifts 
                       WHERE id = @Id";
            return await connection.QueryFirstOrDefaultAsync<EmployeeShift>(sql, new { Id = id });
        }

        public async Task<int> AddAsync(EmployeeShift entity)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"INSERT INTO employee_shifts (employee_id, shift_id, work_date, check_in_time, 
                                                    check_out_time, status) 
                       VALUES (@EmployeeId, @ShiftId, @WorkDate, @CheckInTime, @CheckOutTime, @Status);
                       SELECT LAST_INSERT_ID();";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public async Task<bool> UpdateAsync(EmployeeShift entity)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"UPDATE employee_shifts SET 
                       employee_id = @EmployeeId,
                       shift_id = @ShiftId,
                       work_date = @WorkDate,
                       check_in_time = @CheckInTime,
                       check_out_time = @CheckOutTime,
                       status = @Status
                       WHERE id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, entity);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = "DELETE FROM employee_shifts WHERE id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<EmployeeShift>> GetByEmployeeIdAsync(int employeeId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            using var connection = _dbConnection.CreateConnection();
            var whereClause = "WHERE es.employee_id = @EmployeeId";
            var parameters = new { EmployeeId = employeeId, FromDate = fromDate, ToDate = toDate };
            
            if (fromDate.HasValue)
                whereClause += " AND DATE(es.work_date) >= DATE(@FromDate)";
            if (toDate.HasValue)
                whereClause += " AND DATE(es.work_date) <= DATE(@ToDate)";
            
            var sql = @$"SELECT es.id, es.employee_id, es.shift_id, es.work_date, es.check_in_time, 
                               es.check_out_time, es.status,
                               e.id, e.name, e.phone, e.email, e.role_id, e.hire_date, e.salary, 
                               e.commission_percentage, e.is_active, e.created_at, e.updated_at,
                               s.id, s.name, s.start_time, s.end_time, s.is_active
                        FROM employee_shifts es
                        INNER JOIN employees e ON es.employee_id = e.id
                        INNER JOIN shifts s ON es.shift_id = s.id
                        {whereClause}
                        ORDER BY es.work_date DESC";
            
            var employeeShifts = await connection.QueryAsync<EmployeeShift, Employee, Shift, EmployeeShift>(
                sql,
                (employeeShift, employee, shift) =>
                {
                    employeeShift.Employee = employee;
                    employeeShift.Shift = shift;
                    return employeeShift;
                },
                parameters,
                splitOn: "id,id"
            );
            
            return employeeShifts;
        }

        public async Task<IEnumerable<EmployeeShift>> GetByDateAsync(DateTime date)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT es.id, es.employee_id, es.shift_id, es.work_date, es.check_in_time, 
                              es.check_out_time, es.status,
                              e.id, e.name, e.phone, e.email, e.role_id, e.hire_date, e.salary, 
                              e.commission_percentage, e.is_active, e.created_at, e.updated_at,
                              s.id, s.name, s.start_time, s.end_time, s.is_active
                       FROM employee_shifts es
                       INNER JOIN employees e ON es.employee_id = e.id
                       INNER JOIN shifts s ON es.shift_id = s.id
                       WHERE DATE(es.work_date) = DATE(@Date)
                       ORDER BY s.start_time, e.name";
            
            var employeeShifts = await connection.QueryAsync<EmployeeShift, Employee, Shift, EmployeeShift>(
                sql,
                (employeeShift, employee, shift) =>
                {
                    employeeShift.Employee = employee;
                    employeeShift.Shift = shift;
                    return employeeShift;
                },
                new { Date = date },
                splitOn: "id,id"
            );
            
            return employeeShifts;
        }

        public async Task<IEnumerable<EmployeeShift>> GetCurrentWorkingEmployeesAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT es.id, es.employee_id, es.shift_id, es.work_date, es.check_in_time, 
                              es.check_out_time, es.status,
                              e.id, e.name, e.phone, e.email, e.role_id, e.hire_date, e.salary, 
                              e.commission_percentage, e.is_active, e.created_at, e.updated_at,
                              s.id, s.name, s.start_time, s.end_time, s.is_active
                       FROM employee_shifts es
                       INNER JOIN employees e ON es.employee_id = e.id
                       INNER JOIN shifts s ON es.shift_id = s.id
                       WHERE DATE(es.work_date) = DATE(@Today) 
                         AND es.status = 'CheckedIn'
                       ORDER BY e.name";
            
            var employeeShifts = await connection.QueryAsync<EmployeeShift, Employee, Shift, EmployeeShift>(
                sql,
                (employeeShift, employee, shift) =>
                {
                    employeeShift.Employee = employee;
                    employeeShift.Shift = shift;
                    return employeeShift;
                },
                new { Today = DateTime.Today },
                splitOn: "id,id"
            );
            
            return employeeShifts;
        }

        public async Task<bool> CheckInAsync(int employeeShiftId)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"UPDATE employee_shifts SET 
                       check_in_time = @CheckInTime,
                       status = 'CheckedIn'
                       WHERE id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, new { 
                Id = employeeShiftId, 
                CheckInTime = DateTime.Now 
            });
            return rowsAffected > 0;
        }

        public async Task<bool> CheckOutAsync(int employeeShiftId)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"UPDATE employee_shifts SET 
                       check_out_time = @CheckOutTime,
                       status = 'CheckedOut'
                       WHERE id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, new { 
                Id = employeeShiftId, 
                CheckOutTime = DateTime.Now 
            });
            return rowsAffected > 0;
        }

        public async Task<EmployeeShift?> GetActiveShiftForEmployeeAsync(int employeeId, DateTime date)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT es.id, es.employee_id, es.shift_id, es.work_date, es.check_in_time, 
                              es.check_out_time, es.status,
                              e.id, e.name, e.phone, e.email, e.role_id, e.hire_date, e.salary, 
                              e.commission_percentage, e.is_active, e.created_at, e.updated_at,
                              s.id, s.name, s.start_time, s.end_time, s.is_active
                       FROM employee_shifts es
                       INNER JOIN employees e ON es.employee_id = e.id
                       INNER JOIN shifts s ON es.shift_id = s.id
                       WHERE es.employee_id = @EmployeeId 
                         AND DATE(es.work_date) = DATE(@Date)
                         AND es.status IN ('Scheduled', 'CheckedIn')";
            
            var employeeShifts = await connection.QueryAsync<EmployeeShift, Employee, Shift, EmployeeShift>(
                sql,
                (employeeShift, employee, shift) =>
                {
                    employeeShift.Employee = employee;
                    employeeShift.Shift = shift;
                    return employeeShift;
                },
                new { EmployeeId = employeeId, Date = date },
                splitOn: "id,id"
            );
            
            return employeeShifts.FirstOrDefault();
        }
    }
}
