using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface ITableRepository : IRepository<Table>
    {
        Task<IEnumerable<Table>> GetAvailableTablesAsync();
        Task<bool> UpdateTableStatusAsync(int tableId, TableStatus status);
        Task<Table?> GetTableByNumberAsync(string tableNumber);
    }

    public class TableRepository : BaseRepository<Table>, ITableRepository
    {
        protected override string TableName => "tables";
        protected override string SelectColumns => "id, table_number, capacity, status, is_active";

        public TableRepository(DatabaseConnection dbConnection) : base(dbConnection) { }

        public override async Task<int> AddAsync(Table entity)
        {
            var sql = @"INSERT INTO tables (table_number, capacity, status, is_active) 
                       VALUES (@TableNumber, @Capacity, @Status, @IsActive)";
            return await ExecuteInsertAsync(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Table entity)
        {
            var sql = @"UPDATE tables SET 
                       table_number = @TableNumber, 
                       capacity = @Capacity,
                       status = @Status,
                       is_active = @IsActive
                       WHERE id = @Id";
            return await ExecuteUpdateAsync(sql, entity);
        }

        public async Task<IEnumerable<Table>> GetAvailableTablesAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"SELECT {SelectColumns} FROM {TableName} WHERE status = 'Available' AND is_active = 1 ORDER BY table_number";
            return await connection.QueryAsync<Table>(sql);
        }

        public async Task<bool> UpdateTableStatusAsync(int tableId, TableStatus status)
        {
            var sql = "UPDATE tables SET status = @Status WHERE id = @Id";
            return await ExecuteUpdateAsync(sql, new { Id = tableId, Status = status });
        }

        public async Task<Table?> GetTableByNumberAsync(string tableNumber)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"SELECT {SelectColumns} FROM {TableName} WHERE table_number = @TableNumber";
            return await connection.QueryFirstOrDefaultAsync<Table>(sql, new { TableNumber = tableNumber });
        }
    }
}
