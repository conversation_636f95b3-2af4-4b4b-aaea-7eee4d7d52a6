using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface IDishRepository : IRepository<Dish>
    {
        Task<IEnumerable<Dish>> GetDishesWithCategoryAsync();
        Task<Dish?> GetDishWithIngredientsAsync(int dishId);
        Task<IEnumerable<Dish>> GetDishesByCategoryAsync(int categoryId);
        Task<bool> UpdateDishCostAsync(int dishId, decimal costPrice, decimal sellingPrice);
    }

    public class DishRepository : BaseRepository<Dish>, IDishRepository
    {
        protected override string TableName => "dishes";
        protected override string SelectColumns => @"d.id, d.name, d.category_id, d.description, d.cost_price, 
                                                    d.profit_margin, d.selling_price, d.preparation_time, 
                                                    d.is_active, d.created_at, d.updated_at";

        public DishRepository(DatabaseConnection dbConnection) : base(dbConnection) { }

        public override async Task<int> AddAsync(Dish entity)
        {
            var sql = @"INSERT INTO dishes (name, category_id, description, cost_price, profit_margin, 
                                          selling_price, preparation_time, is_active, created_at, updated_at) 
                       VALUES (@Name, @CategoryId, @Description, @CostPrice, @ProfitMargin, 
                              @SellingPrice, @PreparationTime, @IsActive, @CreatedAt, @UpdatedAt)";
            return await ExecuteInsertAsync(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Dish entity)
        {
            entity.UpdatedAt = DateTime.Now;
            var sql = @"UPDATE dishes SET 
                       name = @Name, 
                       category_id = @CategoryId,
                       description = @Description,
                       cost_price = @CostPrice,
                       profit_margin = @ProfitMargin,
                       selling_price = @SellingPrice,
                       preparation_time = @PreparationTime,
                       is_active = @IsActive,
                       updated_at = @UpdatedAt 
                       WHERE id = @Id";
            return await ExecuteUpdateAsync(sql, entity);
        }

        public async Task<IEnumerable<Dish>> GetDishesWithCategoryAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns}, c.id, c.name, c.description, c.is_active, c.created_at, c.updated_at
                        FROM dishes d
                        INNER JOIN categories c ON d.category_id = c.id
                        WHERE d.is_active = 1
                        ORDER BY c.name, d.name";
            
            var dishes = await connection.QueryAsync<Dish, Category, Dish>(
                sql,
                (dish, category) =>
                {
                    dish.Category = category;
                    return dish;
                },
                splitOn: "id"
            );
            
            return dishes;
        }

        public async Task<Dish?> GetDishWithIngredientsAsync(int dishId)
        {
            using var connection = _dbConnection.CreateConnection();
            
            var dishSql = @$"SELECT {SelectColumns}, c.id, c.name, c.description, c.is_active, c.created_at, c.updated_at
                            FROM dishes d
                            INNER JOIN categories c ON d.category_id = c.id
                            WHERE d.id = @DishId";
            
            var dish = await connection.QueryAsync<Dish, Category, Dish>(
                dishSql,
                (d, category) =>
                {
                    d.Category = category;
                    return d;
                },
                new { DishId = dishId },
                splitOn: "id"
            );
            
            var result = dish.FirstOrDefault();
            if (result == null) return null;

            var ingredientsSql = @"SELECT di.id, di.dish_id, di.ingredient_id, di.quantity, di.cost,
                                         i.id, i.name, i.unit_id, i.cost_per_unit, i.current_stock, i.minimum_stock,
                                         i.is_active, i.created_at, i.updated_at,
                                         u.id, u.name, u.abbreviation, u.is_active
                                  FROM dish_ingredients di
                                  INNER JOIN ingredients i ON di.ingredient_id = i.id
                                  INNER JOIN units u ON i.unit_id = u.id
                                  WHERE di.dish_id = @DishId";
            
            var dishIngredients = await connection.QueryAsync<DishIngredient, Ingredient, Unit, DishIngredient>(
                ingredientsSql,
                (dishIngredient, ingredient, unit) =>
                {
                    ingredient.Unit = unit;
                    dishIngredient.Ingredient = ingredient;
                    return dishIngredient;
                },
                new { DishId = dishId },
                splitOn: "id,id"
            );
            
            result.DishIngredients = dishIngredients.ToList();
            return result;
        }

        public async Task<IEnumerable<Dish>> GetDishesByCategoryAsync(int categoryId)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns}
                        FROM dishes d
                        WHERE d.category_id = @CategoryId AND d.is_active = 1
                        ORDER BY d.name";
            
            return await connection.QueryAsync<Dish>(sql, new { CategoryId = categoryId });
        }

        public async Task<bool> UpdateDishCostAsync(int dishId, decimal costPrice, decimal sellingPrice)
        {
            var sql = @"UPDATE dishes SET 
                       cost_price = @CostPrice,
                       selling_price = @SellingPrice,
                       updated_at = @UpdatedAt 
                       WHERE id = @Id";
            return await ExecuteUpdateAsync(sql, new { 
                Id = dishId, 
                CostPrice = costPrice, 
                SellingPrice = sellingPrice, 
                UpdatedAt = DateTime.Now 
            });
        }
    }
}
