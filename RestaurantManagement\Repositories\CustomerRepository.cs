using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface ICustomerRepository : IRepository<Customer>
    {
        Task<Customer?> GetByPhoneAsync(string phone);
        Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm);
    }

    public class CustomerRepository : BaseRepository<Customer>, ICustomerRepository
    {
        protected override string TableName => "customers";
        protected override string SelectColumns => "id, name, phone, address, email, is_active, created_at, updated_at";

        public CustomerRepository(DatabaseConnection dbConnection) : base(dbConnection) { }

        public override async Task<int> AddAsync(Customer entity)
        {
            var sql = @"INSERT INTO customers (name, phone, address, email, is_active, created_at, updated_at) 
                       VALUES (@Name, @Phone, @Address, @Email, @IsActive, @CreatedAt, @UpdatedAt)";
            return await ExecuteInsertAsync(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Customer entity)
        {
            entity.UpdatedAt = DateTime.Now;
            var sql = @"UPDATE customers SET 
                       name = @Name, 
                       phone = @Phone,
                       address = @Address,
                       email = @Email,
                       is_active = @IsActive,
                       updated_at = @UpdatedAt
                       WHERE id = @Id";
            return await ExecuteUpdateAsync(sql, entity);
        }

        public async Task<Customer?> GetByPhoneAsync(string phone)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"SELECT {SelectColumns} FROM {TableName} WHERE phone = @Phone AND is_active = 1";
            return await connection.QueryFirstOrDefaultAsync<Customer>(sql, new { Phone = phone });
        }

        public async Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns} FROM {TableName} 
                        WHERE is_active = 1 AND (
                            name LIKE @SearchTerm OR 
                            phone LIKE @SearchTerm OR 
                            address LIKE @SearchTerm
                        )
                        ORDER BY name";
            var searchPattern = $"%{searchTerm}%";
            return await connection.QueryAsync<Customer>(sql, new { SearchTerm = searchPattern });
        }
    }
}
