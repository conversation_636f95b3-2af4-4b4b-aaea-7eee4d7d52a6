using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface IOrderItemRepository
    {
        Task<IEnumerable<OrderItem>> GetByOrderIdAsync(int orderId);
        Task<int> AddAsync(OrderItem entity);
        Task<bool> UpdateAsync(OrderItem entity);
        Task<bool> DeleteAsync(int id);
        Task<bool> DeleteByOrderIdAsync(int orderId);
        Task<IEnumerable<OrderItem>> GetOrderItemsWithDishesAsync(int orderId);
    }

    public class OrderItemRepository : IOrderItemRepository
    {
        private readonly DatabaseConnection _dbConnection;

        public OrderItemRepository(DatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<IEnumerable<OrderItem>> GetByOrderIdAsync(int orderId)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT id, order_id, dish_id, quantity, unit_price, total_price, special_instructions
                       FROM order_items 
                       WHERE order_id = @OrderId";
            
            return await connection.QueryAsync<OrderItem>(sql, new { OrderId = orderId });
        }

        public async Task<int> AddAsync(OrderItem entity)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"INSERT INTO order_items (order_id, dish_id, quantity, unit_price, total_price, special_instructions) 
                       VALUES (@OrderId, @DishId, @Quantity, @UnitPrice, @TotalPrice, @SpecialInstructions);
                       SELECT LAST_INSERT_ID();";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public async Task<bool> UpdateAsync(OrderItem entity)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"UPDATE order_items SET 
                       quantity = @Quantity, 
                       unit_price = @UnitPrice,
                       total_price = @TotalPrice,
                       special_instructions = @SpecialInstructions
                       WHERE id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, entity);
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = "DELETE FROM order_items WHERE id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteByOrderIdAsync(int orderId)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = "DELETE FROM order_items WHERE order_id = @OrderId";
            var rowsAffected = await connection.ExecuteAsync(sql, new { OrderId = orderId });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<OrderItem>> GetOrderItemsWithDishesAsync(int orderId)
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @"SELECT oi.id, oi.order_id, oi.dish_id, oi.quantity, oi.unit_price, 
                              oi.total_price, oi.special_instructions,
                              d.id, d.name, d.category_id, d.description, d.cost_price, 
                              d.profit_margin, d.selling_price, d.preparation_time, 
                              d.is_active, d.created_at, d.updated_at
                       FROM order_items oi
                       INNER JOIN dishes d ON oi.dish_id = d.id
                       WHERE oi.order_id = @OrderId";
            
            var orderItems = await connection.QueryAsync<OrderItem, Dish, OrderItem>(
                sql,
                (orderItem, dish) =>
                {
                    orderItem.Dish = dish;
                    return orderItem;
                },
                new { OrderId = orderId },
                splitOn: "id"
            );
            
            return orderItems;
        }
    }
}
