using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface ICategoryRepository : IRepository<Category>
    {
        Task<IEnumerable<Category>> GetActiveCategoriesAsync();
    }

    public class CategoryRepository : BaseRepository<Category>, ICategoryRepository
    {
        protected override string TableName => "categories";
        protected override string SelectColumns => "id, name, description, is_active, created_at, updated_at";

        public CategoryRepository(DatabaseConnection dbConnection) : base(dbConnection) { }

        public override async Task<int> AddAsync(Category entity)
        {
            var sql = @"INSERT INTO categories (name, description, is_active, created_at, updated_at) 
                       VALUES (@Name, @Description, @IsActive, @CreatedAt, @UpdatedAt)";
            return await ExecuteInsertAsync(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Category entity)
        {
            entity.UpdatedAt = DateTime.Now;
            var sql = @"UPDATE categories SET 
                       name = @Name, 
                       description = @Description, 
                       is_active = @IsActive,
                       updated_at = @UpdatedAt 
                       WHERE id = @Id";
            return await ExecuteUpdateAsync(sql, entity);
        }

        public async Task<IEnumerable<Category>> GetActiveCategoriesAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = $"SELECT {SelectColumns} FROM {TableName} WHERE is_active = 1 ORDER BY name";
            return await connection.QueryAsync<Category>(sql);
        }
    }
}
