namespace RestaurantManagement.Models
{
    public class PurchaseItem
    {
        public int Id { get; set; }
        public int PurchaseId { get; set; }
        public int IngredientId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public decimal TotalCost { get; set; }
        
        // Navigation properties
        public Purchase? Purchase { get; set; }
        public Ingredient? Ingredient { get; set; }
        
        // Calculated properties
        public decimal CalculatedTotalCost => Quantity * UnitCost;
    }
}
