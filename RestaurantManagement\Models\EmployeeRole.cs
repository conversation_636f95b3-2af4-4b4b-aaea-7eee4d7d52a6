using System.Collections.Generic;

namespace RestaurantManagement.Models
{
    public class EmployeeRole
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public List<string> Permissions { get; set; } = new();
        public bool IsActive { get; set; } = true;
        
        public bool HasPermission(string permission)
        {
            return Permissions.Contains("all") || Permissions.Contains(permission);
        }
    }
}
