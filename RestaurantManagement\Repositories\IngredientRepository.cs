using Dapper;
using RestaurantManagement.Data;
using RestaurantManagement.Models;

namespace RestaurantManagement.Repositories
{
    public interface IIngredientRepository : IRepository<Ingredient>
    {
        Task<IEnumerable<Ingredient>> GetIngredientsWithUnitsAsync();
        Task<IEnumerable<Ingredient>> GetLowStockIngredientsAsync();
        Task<bool> UpdateStockAsync(int ingredientId, decimal newStock);
        Task<IEnumerable<Ingredient>> GetActiveIngredientsAsync();
    }

    public class IngredientRepository : BaseRepository<Ingredient>, IIngredientRepository
    {
        protected override string TableName => "ingredients";
        protected override string SelectColumns => @"i.id, i.name, i.unit_id, i.cost_per_unit, i.current_stock, 
                                                    i.minimum_stock, i.is_active, i.created_at, i.updated_at";

        public IngredientRepository(DatabaseConnection dbConnection) : base(dbConnection) { }

        public override async Task<int> AddAsync(Ingredient entity)
        {
            var sql = @"INSERT INTO ingredients (name, unit_id, cost_per_unit, current_stock, minimum_stock, 
                                               is_active, created_at, updated_at) 
                       VALUES (@Name, @UnitId, @CostPerUnit, @CurrentStock, @MinimumStock, 
                              @IsActive, @CreatedAt, @UpdatedAt)";
            return await ExecuteInsertAsync(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Ingredient entity)
        {
            entity.UpdatedAt = DateTime.Now;
            var sql = @"UPDATE ingredients SET 
                       name = @Name, 
                       unit_id = @UnitId,
                       cost_per_unit = @CostPerUnit,
                       current_stock = @CurrentStock,
                       minimum_stock = @MinimumStock,
                       is_active = @IsActive,
                       updated_at = @UpdatedAt 
                       WHERE id = @Id";
            return await ExecuteUpdateAsync(sql, entity);
        }

        public async Task<IEnumerable<Ingredient>> GetIngredientsWithUnitsAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns}, u.id, u.name, u.abbreviation, u.is_active
                        FROM ingredients i
                        INNER JOIN units u ON i.unit_id = u.id
                        WHERE i.is_active = 1
                        ORDER BY i.name";
            
            var ingredients = await connection.QueryAsync<Ingredient, Unit, Ingredient>(
                sql,
                (ingredient, unit) =>
                {
                    ingredient.Unit = unit;
                    return ingredient;
                },
                splitOn: "id"
            );
            
            return ingredients;
        }

        public async Task<IEnumerable<Ingredient>> GetLowStockIngredientsAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns}, u.id, u.name, u.abbreviation, u.is_active
                        FROM ingredients i
                        INNER JOIN units u ON i.unit_id = u.id
                        WHERE i.is_active = 1 AND i.current_stock <= i.minimum_stock
                        ORDER BY i.name";
            
            var ingredients = await connection.QueryAsync<Ingredient, Unit, Ingredient>(
                sql,
                (ingredient, unit) =>
                {
                    ingredient.Unit = unit;
                    return ingredient;
                },
                splitOn: "id"
            );
            
            return ingredients;
        }

        public async Task<bool> UpdateStockAsync(int ingredientId, decimal newStock)
        {
            var sql = @"UPDATE ingredients SET 
                       current_stock = @NewStock,
                       updated_at = @UpdatedAt 
                       WHERE id = @Id";
            return await ExecuteUpdateAsync(sql, new { Id = ingredientId, NewStock = newStock, UpdatedAt = DateTime.Now });
        }

        public async Task<IEnumerable<Ingredient>> GetActiveIngredientsAsync()
        {
            using var connection = _dbConnection.CreateConnection();
            var sql = @$"SELECT {SelectColumns}, u.id, u.name, u.abbreviation, u.is_active
                        FROM ingredients i
                        INNER JOIN units u ON i.unit_id = u.id
                        WHERE i.is_active = 1
                        ORDER BY i.name";
            
            var ingredients = await connection.QueryAsync<Ingredient, Unit, Ingredient>(
                sql,
                (ingredient, unit) =>
                {
                    ingredient.Unit = unit;
                    return ingredient;
                },
                splitOn: "id"
            );
            
            return ingredients;
        }
    }
}
