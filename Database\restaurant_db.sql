-- Restaurant Management System Database
-- Created for C# WinForms Application with Dapper ORM

CREATE DATABASE IF NOT EXISTS restaurant_management;
USE restaurant_management;

-- Table: Categories (فئات الأطباق)
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table: Units (وحدات القياس)
CREATE TABLE units (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    abbreviation VARCHAR(10) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- Table: Ingredients (المكونات/المواد الخام)
CREATE TABLE ingredients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    unit_id INT NOT NULL,
    cost_per_unit DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    current_stock DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    minimum_stock DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (unit_id) REFERENCES units(id)
);

-- Table: Dishes (الأطباق)
CREATE TABLE dishes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    category_id INT NOT NULL,
    description TEXT,
    cost_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    profit_margin DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    selling_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    preparation_time INT DEFAULT 0, -- in minutes
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- Table: Dish Ingredients (مكونات الأطباق)
CREATE TABLE dish_ingredients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    dish_id INT NOT NULL,
    ingredient_id INT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    FOREIGN KEY (dish_id) REFERENCES dishes(id) ON DELETE CASCADE,
    FOREIGN KEY (ingredient_id) REFERENCES ingredients(id),
    UNIQUE KEY unique_dish_ingredient (dish_id, ingredient_id)
);

-- Table: Employee Roles (أدوار الموظفين)
CREATE TABLE employee_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    permissions JSON,
    is_active BOOLEAN DEFAULT TRUE
);

-- Table: Employees (الموظفين)
CREATE TABLE employees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    role_id INT NOT NULL,
    hire_date DATE NOT NULL,
    salary DECIMAL(10,2) DEFAULT 0.00,
    commission_percentage DECIMAL(5,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES employee_roles(id)
);

-- Table: Shifts (الشيفتات)
CREATE TABLE shifts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- Table: Employee Shifts (شيفتات الموظفين)
CREATE TABLE employee_shifts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    shift_id INT NOT NULL,
    work_date DATE NOT NULL,
    check_in_time TIMESTAMP NULL,
    check_out_time TIMESTAMP NULL,
    status ENUM('scheduled', 'checked_in', 'checked_out', 'absent') DEFAULT 'scheduled',
    FOREIGN KEY (employee_id) REFERENCES employees(id),
    FOREIGN KEY (shift_id) REFERENCES shifts(id),
    UNIQUE KEY unique_employee_shift_date (employee_id, shift_id, work_date)
);

-- Table: Tables (الطاولات)
CREATE TABLE tables (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_number VARCHAR(10) NOT NULL UNIQUE,
    capacity INT NOT NULL DEFAULT 4,
    status ENUM('available', 'occupied', 'reserved', 'out_of_order') DEFAULT 'available',
    is_active BOOLEAN DEFAULT TRUE
);

-- Table: Customers (العملاء)
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table: Orders (الطلبات)
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(20) NOT NULL UNIQUE,
    order_type ENUM('dine_in', 'take_away', 'delivery') NOT NULL,
    table_id INT NULL,
    customer_id INT NULL,
    employee_id INT NOT NULL,
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('new', 'preparing', 'ready', 'delivered', 'cancelled') DEFAULT 'new',
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    notes TEXT,
    delivery_address TEXT,
    delivery_phone VARCHAR(20),
    FOREIGN KEY (table_id) REFERENCES tables(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (employee_id) REFERENCES employees(id)
);

-- Table: Order Items (عناصر الطلبات)
CREATE TABLE order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    dish_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    special_instructions TEXT,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (dish_id) REFERENCES dishes(id)
);

-- Table: Stock Transactions (حركات المخزون)
CREATE TABLE stock_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ingredient_id INT NOT NULL,
    transaction_type ENUM('purchase', 'usage', 'adjustment', 'waste') NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_cost DECIMAL(10,2) DEFAULT 0.00,
    total_cost DECIMAL(10,2) DEFAULT 0.00,
    reference_type ENUM('order', 'purchase', 'manual') DEFAULT 'manual',
    reference_id INT NULL,
    notes TEXT,
    employee_id INT NOT NULL,
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ingredient_id) REFERENCES ingredients(id),
    FOREIGN KEY (employee_id) REFERENCES employees(id)
);

-- Table: Suppliers (الموردين)
CREATE TABLE suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table: Purchases (المشتريات)
CREATE TABLE purchases (
    id INT AUTO_INCREMENT PRIMARY KEY,
    supplier_id INT NOT NULL,
    purchase_date DATE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    status ENUM('pending', 'received', 'cancelled') DEFAULT 'pending',
    notes TEXT,
    employee_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (employee_id) REFERENCES employees(id)
);

-- Table: Purchase Items (عناصر المشتريات)
CREATE TABLE purchase_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    purchase_id INT NOT NULL,
    ingredient_id INT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_cost DECIMAL(10,2) NOT NULL,
    total_cost DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (purchase_id) REFERENCES purchases(id) ON DELETE CASCADE,
    FOREIGN KEY (ingredient_id) REFERENCES ingredients(id)
);

-- Table: Employee Sales (مبيعات الموظفين للبرسنتاج)
CREATE TABLE employee_sales (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    order_id INT NOT NULL,
    sale_amount DECIMAL(10,2) NOT NULL,
    commission_percentage DECIMAL(5,2) NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL,
    sale_date DATE NOT NULL,
    FOREIGN KEY (employee_id) REFERENCES employees(id),
    FOREIGN KEY (order_id) REFERENCES orders(id),
    UNIQUE KEY unique_employee_order (employee_id, order_id)
);

-- Insert default data
INSERT INTO units (name, abbreviation) VALUES
('كيلوجرام', 'كجم'),
('جرام', 'جم'),
('لتر', 'لتر'),
('مليلتر', 'مل'),
('قطعة', 'قطعة'),
('كوب', 'كوب'),
('ملعقة كبيرة', 'م.ك'),
('ملعقة صغيرة', 'م.ص');

INSERT INTO employee_roles (name, permissions) VALUES
('مدير', '["all"]'),
('نادل', '["orders", "tables"]'),
('كاشير', '["orders", "payments"]'),
('طباخ', '["kitchen", "orders_view"]'),
('مساعد', '["basic"]');

INSERT INTO categories (name, description) VALUES
('بيتزا', 'جميع أنواع البيتزا'),
('مقبلات', 'المقبلات والسلطات'),
('مشروبات', 'المشروبات الباردة والساخنة'),
('حلويات', 'الحلويات والآيس كريم'),
('وجبات رئيسية', 'الوجبات الرئيسية');

INSERT INTO shifts (name, start_time, end_time) VALUES
('الصباحي', '08:00:00', '16:00:00'),
('المسائي', '16:00:00', '00:00:00'),
('الليلي', '00:00:00', '08:00:00');

INSERT INTO tables (table_number, capacity) VALUES
('T01', 2), ('T02', 4), ('T03', 4), ('T04', 6), ('T05', 2),
('T06', 4), ('T07', 4), ('T08', 8), ('T09', 2), ('T10', 4);
